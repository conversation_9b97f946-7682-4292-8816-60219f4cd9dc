'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea'; // Added Textarea
import { Trash2, MessageSquare, ThumbsUp, BarChart2, Send, Loader2, RefreshCw, ImageIcon } from 'lucide-react'; // Added Send, Loader2, RefreshCw, ImageIcon
import { formatDistanceToNow } from 'date-fns';
import { ImageGenerationButton } from '@/components/ui/image-generation-button';

interface Tweet {
  id: number;
  tweet_id: string;
  author_handle: string;
  content: string;
  fetched_at: string;
  tweet_created_at: string;
  sentiment_score: number | null;
  importance_score: number | null;
  is_marked_irrelevant: boolean;
  raw_data: Record<string, unknown>;
  ai_analysis?: {
    relevance_score?: number;
    worth_replying?: boolean;
    evaluation_reason?: string;
    reply_suggestions?: string[];
    analyzed_at?: string;
    last_generated_at?: string;
  };
}

interface TweetCardProps {
  tweet: Tweet;
  isIrrelevant?: boolean;
  onMarkIrrelevant: () => void;
  showEngagementScore?: boolean;
}

export default function TweetCard({ tweet, isIrrelevant = false, onMarkIrrelevant, showEngagementScore = false }: TweetCardProps) {
  const [isGeneratingResponse, setIsGeneratingResponse] = useState(false);
  const [generatedResponses, setGeneratedResponses] = useState<string[] | null>(null);
  const [selectedResponse, setSelectedResponse] = useState<string | null>(null);
  const [editableResponse, setEditableResponse] = useState<string>('');
  const [isPostingReply] = useState(false);

  const handlePostReply = (replyText: string | null) => {
    if (!replyText) {
      alert('No reply text available to prepare.');
      return;
    }
    if (!tweet.tweet_id) {
      alert('Original tweet ID is missing.');
      return;
    }

    const encodedReplyText = encodeURIComponent(replyText);
    const twitterIntentUrl = `https://twitter.com/intent/tweet?in_reply_to=${tweet.tweet_id}&text=${encodedReplyText}`;

    window.open(twitterIntentUrl, '_blank', 'noopener,noreferrer');
    // No need for isPostingReply state for this approach
  };

  const handleGenerateResponse = async () => {
    try {
      setIsGeneratingResponse(true);
      setGeneratedResponses(null); // Clear previous suggestions
      setSelectedResponse(null);
      setEditableResponse('');

      const response = await fetch('/api/ai/generate-reply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tweet_id: tweet.id, count: 3 }), // Request 3 suggestions
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate response');
      }

      const data = await response.json();
      let actualSuggestions = (data.suggestions || []).slice(0, 3); // Ensure we take at most 3

      // Filter out common introductory phrases
      actualSuggestions = actualSuggestions.filter((s: string) => s && !s.toLowerCase().startsWith("here are") && s.trim() !== "");

      if (actualSuggestions.length > 0) {
        setGeneratedResponses(actualSuggestions);
        setSelectedResponse(actualSuggestions[0]);
        setEditableResponse(actualSuggestions[0]);

        // Update the tweet object with the filtered suggestions
        if (!tweet.ai_analysis) {
          tweet.ai_analysis = {};
        }
        tweet.ai_analysis.reply_suggestions = actualSuggestions;
        tweet.ai_analysis.last_generated_at = new Date().toISOString();
      } else {
        setEditableResponse('No valid responses could be generated.');
        setGeneratedResponses([]); // Set to empty array to indicate generation happened but no results
      }
    } catch (error) {
      console.error('Error generating response:', error);
      setEditableResponse(
        `Error generating reply. Fallback: Thank you for your tweet about ${tweet.content.split(' ').slice(0, 3).join(' ')}...`
      );
      setGeneratedResponses([]); // Set to empty array
    } finally {
      setIsGeneratingResponse(false);
    }
  };

  const handleSelectSuggestion = (suggestion: string) => {
    setSelectedResponse(suggestion);
    setEditableResponse(suggestion);
  };



  const getSentimentLabel = (score: number | null) => {
    if (score === null) return null;
    if (score > 0.3) return { label: 'Positive', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' };
    if (score < -0.3) return { label: 'Negative', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' };
    return { label: 'Neutral', color: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300' };
  };

  const getImportanceLabel = (score: number | null) => {
    if (score === null) return null;
    if (score > 7) return { label: 'High', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' };
    if (score > 4) return { label: 'Medium', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' };
    return { label: 'Low', color: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300' };
  };

  const sentiment = getSentimentLabel(tweet.sentiment_score);
  const importance = getImportanceLabel(tweet.importance_score);

  // Calculate total engagement score
  const metrics = tweet.raw_data?.public_metrics || {};
  const totalEngagement = (metrics.retweet_count || 0) +
                         (metrics.reply_count || 0) +
                         (metrics.like_count || 0) +
                         (metrics.quote_count || 0);

  return (
    <Card className={isIrrelevant ? 'opacity-60' : ''}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <a
              href={`https://twitter.com/${tweet.author_handle}`}
              target="_blank"
              rel="noopener noreferrer"
              className="font-semibold hover:underline"
            >
              @{tweet.author_handle}
            </a>
            <p className="text-sm text-muted-foreground">
              {tweet.tweet_created_at ?
                formatDistanceToNow(new Date(tweet.tweet_created_at), { addSuffix: true }) :
                'Unknown date'}
            </p>
          </div>
          <div className="flex gap-2">
            {sentiment && (
              <Badge variant="outline" className={sentiment.color}>
                {sentiment.label}
              </Badge>
            )}
            {importance && (
              <Badge variant="outline" className={importance.color}>
                {importance.label}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <a
          href={`https://twitter.com/${tweet.author_handle}/status/${tweet.tweet_id}`}
          target="_blank"
          rel="noopener noreferrer"
          className="hover:underline"
        >
          <p className="whitespace-pre-wrap">{tweet.content}</p>
        </a>

        {/* AI Analysis */}
        {tweet.ai_analysis?.worth_replying !== undefined && (
          <div className={`mt-4 p-3 rounded-md ${tweet.ai_analysis.worth_replying ? 'bg-primary/10 border border-primary/30' : 'bg-muted border border-secondary'}`}>
            <div className="flex justify-between items-center">
              <p className="text-sm font-semibold">
                Relevant: <span className="font-bold">{tweet.ai_analysis.worth_replying ? 'YES' : 'NO'}</span>
              </p>
              <Badge
                variant="outline"
                className={`text-xs ${tweet.ai_analysis.worth_replying ? 'text-primary-foreground bg-primary/80' : 'text-secondary-foreground bg-secondary/80'}`}
              >
                {tweet.ai_analysis.worth_replying ? 'Worth Replying' : 'Skip'}
              </Badge>
            </div>
          </div>
        )}

        {/* Display Generated Suggestions */}
        {generatedResponses && generatedResponses.length > 0 && (
          <div className="mt-4 p-3 bg-muted rounded-md space-y-2">
            <p className="text-sm font-semibold mb-1 text-foreground">Suggested Responses:</p>
            {generatedResponses.map((suggestion, index) => (
              <div key={index} className={`p-2 rounded-md border flex justify-between items-center transition-all ${selectedResponse === suggestion ? 'bg-primary/10 border-primary' : 'bg-card hover:bg-primary/5'}`}>
                <p className="text-sm flex-grow mr-2 text-card-foreground">{suggestion}</p>
                <Button
                  variant={selectedResponse === suggestion ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleSelectSuggestion(suggestion)}
                  className="ml-auto flex-shrink-0"
                >
                  <Send className="mr-1 h-3 w-3" />
                  Use
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Editable Text Area - shows after generation attempt, even if no suggestions */}
        {(generatedResponses !== null || editableResponse) && (
          <div className="mt-4">
            <Textarea
              value={editableResponse}
              onChange={(e) => setEditableResponse(e.target.value)}
              placeholder="Edit your reply here..."
              className="min-h-[80px] text-sm"
            />
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          {tweet.raw_data?.public_metrics && (
            <>
              <span className="flex items-center">
                <MessageSquare className="mr-1 h-4 w-4" />
                {tweet.raw_data.public_metrics.reply_count || 0}
              </span>
              <span className="flex items-center">
                <ThumbsUp className="mr-1 h-4 w-4" />
                {tweet.raw_data.public_metrics.like_count || 0}
              </span>
              <span className="flex items-center">
                <BarChart2 className="mr-1 h-4 w-4" />
                {tweet.raw_data.public_metrics.retweet_count || 0}
              </span>
              {showEngagementScore && totalEngagement > 0 && (
                <span className="flex items-center font-medium text-primary">
                  Total: {totalEngagement}
                </span>
              )}
            </>
          )}
        </div>
        <div className="flex space-x-2">
          {!isIrrelevant && (
            <>
              {tweet.ai_analysis?.worth_replying && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleGenerateResponse}
                    disabled={isGeneratingResponse}
                  >
                    {isGeneratingResponse ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : generatedResponses === null ? (
                      'Generate'
                    ) : (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Re-generate
                      </>
                    )}
                  </Button>

                  {/* "Answer with this" button, active if there's editable content */}
                  {(generatedResponses !== null || editableResponse) && (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handlePostReply(editableResponse)}
                      disabled={isPostingReply || !editableResponse.trim()}
                    >
                      {isPostingReply ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Send className="mr-2 h-4 w-4" />
                      )}
                      Answer with this
                    </Button>
                  )}
                </>
              )}

              {/* Image Generation Button */}
              <ImageGenerationButton
                contextType="tweet"
                contextContent={tweet.content}
                defaultPrompt={`Create an image related to: ${tweet.content.substring(0, 100)}${tweet.content.length > 100 ? '...' : ''}`}
                tweetId={tweet.tweet_id}
                variant="outline"
                size="sm"
              >
                <ImageIcon className="h-4 w-4" />
              </ImageGenerationButton>

              <Button
                variant="ghost"
                size="icon"
                onClick={onMarkIrrelevant}
                title="Mark as irrelevant"
              >
                <Trash2 className="h-4 w-4 text-red-500" />
              </Button>
            </>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image'; // Import Next Image
import { usePathname } from 'next/navigation';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { User, LogOut, Menu, X, Settings2 } from 'lucide-react'; // Added Settings2 for AI Prefs
import { WalletConnectButton } from '@/components/wallet-connect-button';
import { NetworkSwitcher } from '@/components/network-switcher';
import UserPreferencesForm from '@/components/user-preferences-form'; // Import the form

export default function Navbar() {
  const pathname = usePathname();
  const supabase = createSupabaseBrowserClient();
  const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isPreferencesModalOpen, setIsPreferencesModalOpen] = useState(false); // State for modal

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setIsLoggedIn(!!session);
    };

    checkAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setIsLoggedIn(!!session);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    window.location.href = '/login';
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navItems = [
    { name: 'Home', href: '/' },
  ];

  const authenticatedNavItems = [
    { name: 'Dashboard', href: '/dashboard' },
    { name: 'Personality', href: '/personality' },
    { name: 'Copium', href: '/copy-ai' },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background border-b border-border">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Image src="/logo.png" alt="BuddyChip Logo" width={40} height={40} />
            <span className="font-bold text-xl text-foreground">BuddyChip</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            {/* Show Home button only when not logged in */}
            {!isLoggedIn && navItems.map((item) => {
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    pathname === item.href
                      ? 'bg-primary/10 text-primary'
                      : 'text-foreground hover:bg-muted'
                  }`}
                >
                  {item.name}
                </Link>
              );
            })}

            {isLoggedIn && authenticatedNavItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  pathname === item.href
                    ? 'bg-primary/10 text-primary'
                    : 'text-foreground hover:bg-muted'
                }`}
              >
                {item.name}
              </Link>
            ))}

            {/* Network Switcher - only show when logged in */}
            {isLoggedIn && (
              <NetworkSwitcher variant="compact" />
            )}

            {/* Wallet Connect Button - only show when logged in */}
            {isLoggedIn && (
              <WalletConnectButton variant="desktop" />
            )}

            {isLoggedIn === null ? (
              // Loading state
              <div className="h-9 w-20 bg-muted animate-pulse rounded-md"></div>
            ) : isLoggedIn ? (
              // User dropdown for logged in users
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <User className="h-5 w-5" />
                    <span className="sr-only">User menu</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <Dialog open={isPreferencesModalOpen} onOpenChange={setIsPreferencesModalOpen}>
                    <DialogTrigger asChild>
                      <DropdownMenuItem onSelect={(event) => event.preventDefault()}>
                        <Settings2 className="mr-2 h-4 w-4" />
                        <span>AI Preferences</span>
                      </DropdownMenuItem>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[600px] max-h-[85vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>AI Reply Preferences</DialogTitle>
                        <DialogDescription>
                          Customize how the AI analyzes tweets and generates replies for you.
                          Click &quot;Save Preferences&quot; to apply your changes.
                        </DialogDescription>
                      </DialogHeader>
                      <UserPreferencesForm onSaved={() => setIsPreferencesModalOpen(false)} />
                      {/* <DialogFooter>
                        <Button variant="outline" onClick={() => setIsPreferencesModalOpen(false)}>Close</Button>
                      </DialogFooter> */}
                    </DialogContent>
                  </Dialog>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Sign out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              // Auth buttons for logged out users
              <div className="flex items-center space-x-2">
                <Button variant="ghost" asChild>
                  <Link href="/login">Log in</Link>
                </Button>
                <Button asChild>
                  <Link href="/signup">Sign up</Link>
                </Button>
              </div>
            )}
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={toggleMenu}>
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-border bg-background">
          {/* Show Home button only when not logged in */}
          {!isLoggedIn && navItems.map((item) => {
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  pathname === item.href
                    ? 'bg-primary/10 text-primary'
                    : 'text-foreground hover:bg-muted'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.name}
              </Link>
            );
          })}

          {isLoggedIn && authenticatedNavItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                pathname === item.href
                  ? 'bg-primary/10 text-primary'
                  : 'text-foreground hover:bg-muted'
              }`}
              onClick={() => setIsMenuOpen(false)}
            >
              {item.name}
            </Link>
          ))}

          {/* Network Switcher for Mobile - only show when logged in */}
          {isLoggedIn && (
            <div className="px-3 py-2">
              <NetworkSwitcher variant="default" className="w-full" />
            </div>
          )}

          {/* Wallet Connect Button for Mobile - only show when logged in */}
          {isLoggedIn && (
            <div className="px-3 py-2">
              <WalletConnectButton variant="mobile" />
            </div>
          )}

          {isLoggedIn ? (
            <>
              {/* AI Preferences Modal Trigger for Mobile */}
              <Dialog open={isPreferencesModalOpen} onOpenChange={setIsPreferencesModalOpen}>
                <DialogTrigger asChild>
                  <button
                    className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-foreground hover:bg-muted"
                    onClick={() => {
                      // setIsMenuOpen(false); // Keep menu open until modal is interacted with or explicitly closed
                    }}
                  >
                    <Settings2 className="inline-block mr-2 h-4 w-4" /> AI Preferences
                  </button>
                </DialogTrigger>
                {/* DialogContent is defined above with the desktop trigger, no need to repeat fully,
                    but ensure it's part of the Dialog structure accessible here.
                    For simplicity, the Dialog state is shared.
                    A more complex setup might have separate Dialog instances if needed.
                */}
              </Dialog>

              <Button
                variant="ghost"
                className="w-full justify-start text-foreground hover:bg-muted"
                onClick={() => {
                  handleSignOut();
                  setIsMenuOpen(false);
                }}
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sign out</span>
              </Button>
            </>
          ) : (
            <>
              <Link
                href="/login"
                className="block px-3 py-2 rounded-md text-base font-medium text-foreground hover:bg-muted"
                onClick={() => setIsMenuOpen(false)}
              >
                Log in
              </Link>
              <Link
                href="/signup"
                className="block px-3 py-2 rounded-md text-base font-medium bg-primary text-primary-foreground hover:bg-primary/90"
                onClick={() => setIsMenuOpen(false)}
              >
                Sign up
              </Link>
            </>
          )}
        </div>
      )}
    </header>
  );
}

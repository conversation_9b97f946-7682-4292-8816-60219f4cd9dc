/**
 * Layout Wrapper Component
 *
 * A client-side wrapper component that handles the ErrorBoundary and other
 * client-side providers for the application layout.
 */

"use client";

import React, { useState, useEffect } from "react";
import { ThemeProvider } from "@/components/theme-provider";
import { ToastProvider } from "@/components/ui/toast-provider";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { Web3Provider } from "@/components/web3-provider";
import { NetworkProvider } from "@/contexts/network-context";
import { logger } from "@/lib/utils/logger";
import Navbar from "@/components/navbar";
import { FloatingChatbot } from "@/components/ai/floating-chatbot";

interface LayoutWrapperProps {
  children: React.ReactNode;
}

/**
 * Client-side layout wrapper that provides theme, toast, and error boundary context
 */
export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const [isMounted, setIsMounted] = useState(false);

  // Ensure component only renders on client side to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem={false}
      disableTransitionOnChange
    >
      <NetworkProvider>
        <Web3Provider>
          <ErrorBoundary
            onError={(error, errorInfo) => {
              logger.error("Global error boundary caught an error", error, {
                componentStack: errorInfo.componentStack,
              });
            }}
          >
            <ToastProvider />
            <Navbar />
            <main className="min-h-screen pt-16">
              {children}
            </main>
            {/* Only render FloatingChatbot on client side to prevent hydration issues */}
            {isMounted && <FloatingChatbot />}
          </ErrorBoundary>
        </Web3Provider>
      </NetworkProvider>
    </ThemeProvider>
  );
}

'use client';

import { useEffect, Suspense, lazy } from 'react';
import { useRouter } from 'next/navigation';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { ensureProfile } from '@/lib/utils/ensure-profile';

// Lazy load heavy components
const AIAnalyzedTweets = lazy(() => import('@/components/ai-analyzed-tweets'));
const FollowedAccountsList = lazy(() => import('@/components/followed-accounts-list'));
const AddTwitterAccountForm = lazy(() => import('@/components/add-twitter-account-form'));
const IngestTweetsButton = lazy(() => import('@/components/ingest-tweets-button'));
const AnalyzeUnanalyzedTweetsButton = lazy(() => import('@/components/analyze-unanalyzed-tweets-button'));

// Loading component for heavy sections
const DashboardSkeleton = () => (
  <div className="space-y-8">
    <Skeleton className="h-8 w-64" />
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
      <div className="lg:col-span-1">
        <Skeleton className="h-48 w-full" />
      </div>
      <div className="lg:col-span-3">
        <Skeleton className="h-96 w-full" />
      </div>
    </div>
  </div>
);

// Custom hook for user data
const useUserData = () => {
  const supabase = createSupabaseBrowserClient();

  return useQuery({
    queryKey: ['user-profile'],
    queryFn: async () => {
      console.log('🔍 Fetching user data...');

      // Check if user is authenticated
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        throw new Error('Not authenticated');
      }

      // Ensure profile exists
      try {
        await ensureProfile(supabase, user.id);
      } catch (error) {
        console.error('Error ensuring profile:', error);
      }

      // Fetch user profile
      const { data: profile } = await supabase
        .from('buddychip_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      console.log('✅ User data fetched successfully');
      return { user, profile };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });
};

export default function DashboardPage() {
  const router = useRouter();
  const { data, isLoading, error } = useUserData();

  // Redirect if not authenticated
  useEffect(() => {
    if (error && error.message === 'Not authenticated') {
      router.push('/login');
    }
  }, [error, router]);

  if (isLoading) {
    return <DashboardSkeleton />;
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Dashboard</h1>
          <p className="text-muted-foreground">{error.message}</p>
        </div>
      </div>
    );
  }

  const { user, profile } = data;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Dashboard</h1>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar with user info */}
        <div className="lg:col-span-1">
          <div className="bg-card rounded-lg shadow p-6 sticky top-20">
            <h2 className="text-xl font-semibold mb-4">Your Profile</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">Email</p>
                <p>{user.email}</p>
              </div>
              {profile?.username && (
                <div>
                  <p className="text-sm text-muted-foreground">Username</p>
                  <p>{profile.username}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:col-span-3 space-y-8">
          <Tabs defaultValue="tweets" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="tweets">Tweets</TabsTrigger>
              <TabsTrigger value="accounts">Accounts</TabsTrigger>
            </TabsList>

            <TabsContent value="tweets" className="space-y-8 mt-6">
              {/* AI analyzed tweets */}
              <div className="bg-card rounded-lg shadow p-6">
                <Suspense fallback={<Skeleton className="h-64 w-full" />}>
                  <AIAnalyzedTweets />
                </Suspense>
              </div>

              {/* Ingest tweets button */}
              <div className="bg-card rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Ingest Tweets</h2>
                <p className="text-sm text-muted-foreground mb-4">
                  Click the button below to fetch tweets from the accounts you follow.
                  Only the 10 most recent tweets will be retrieved for each account to keep your collection focused and relevant.
                </p>
                <Suspense fallback={<Skeleton className="h-12 w-full" />}>
                  <IngestTweetsButton />
                </Suspense>
              </div>

              {/* Backup button for analyzing unanalyzed tweets */}
              <div className="bg-card rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Analyze Unanalyzed Tweets</h2>
                <p className="text-sm text-muted-foreground mb-4">
                  If some tweets weren&apos;t automatically analyzed during ingestion, you can use this button to analyze them.
                  By default, this will analyze up to 10 unanalyzed tweets at a time. Check the box to continue analyzing
                  all tweets in batches of 10 until complete.
                </p>
                <Suspense fallback={<Skeleton className="h-12 w-full" />}>
                  <AnalyzeUnanalyzedTweetsButton />
                </Suspense>
              </div>
            </TabsContent>

            <TabsContent value="accounts" className="space-y-8 mt-6">
              {/* Add Twitter account form */}
              <div className="bg-card rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Add Twitter Account to Follow</h2>
                <Suspense fallback={<Skeleton className="h-24 w-full" />}>
                  <AddTwitterAccountForm />
                </Suspense>
              </div>

              {/* Followed accounts list */}
              <div className="bg-card rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Followed Accounts</h2>
                <Suspense fallback={<Skeleton className="h-48 w-full" />}>
                  <FollowedAccountsList />
                </Suspense>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

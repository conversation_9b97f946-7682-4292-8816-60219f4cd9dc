import { createSupabaseServerClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import FollowedAccountsList from '@/components/followed-accounts-list';
import AddTwitterAccountForm from '@/components/add-twitter-account-form';
import AIAnalyzedTweets from '@/components/ai-analyzed-tweets';
import IngestTweetsButton from '@/components/ingest-tweets-button';
import AnalyzeUnanalyzedTweetsButton from '@/components/analyze-unanalyzed-tweets-button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ensureProfile } from '@/lib/utils/ensure-profile';

export default async function DashboardPage() {
  const supabase = await createSupabaseServerClient();

  // Check if user is authenticated
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    // Redirect to login if not authenticated
    redirect('/login');
  }

  // Ensure profile exists
  try {
    await ensureProfile(supabase, user.id);
  } catch (error) {
    console.error('Error ensuring profile:', error);
  }

  // Fetch user profile
  const { data: profile } = await supabase
    .from('buddychip_profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Dashboard</h1>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar with user info */}
        <div className="lg:col-span-1">
          <div className="bg-card rounded-lg shadow p-6 sticky top-20">
            <h2 className="text-xl font-semibold mb-4">Your Profile</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">Email</p>
                <p>{user.email}</p>
              </div>
              {profile?.username && (
                <div>
                  <p className="text-sm text-muted-foreground">Username</p>
                  <p>{profile.username}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:col-span-3 space-y-8">
          <Tabs defaultValue="tweets" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="tweets">Tweets</TabsTrigger>
              <TabsTrigger value="accounts">Accounts</TabsTrigger>
            </TabsList>

            <TabsContent value="tweets" className="space-y-8 mt-6">
              {/* AI analyzed tweets */}
              <div className="bg-card rounded-lg shadow p-6">
                <AIAnalyzedTweets />
              </div>

              {/* Ingest tweets button */}
              <div className="bg-card rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Ingest Tweets</h2>
                <p className="text-sm text-muted-foreground mb-4">
                  Click the button below to fetch tweets from the accounts you follow.
                  Only the 10 most recent tweets will be retrieved for each account to keep your collection focused and relevant.
                </p>
                <IngestTweetsButton />
              </div>

              {/* Backup button for analyzing unanalyzed tweets */}
              <div className="bg-card rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Analyze Unanalyzed Tweets</h2>
                <p className="text-sm text-muted-foreground mb-4">
                  If some tweets weren&apos;t automatically analyzed during ingestion, you can use this button to analyze them.
                  By default, this will analyze up to 10 unanalyzed tweets at a time. Check the box to continue analyzing
                  all tweets in batches of 10 until complete.
                </p>
                <AnalyzeUnanalyzedTweetsButton />
              </div>
            </TabsContent>

            <TabsContent value="accounts" className="space-y-8 mt-6">
              {/* Add Twitter account form */}
              <div className="bg-card rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Add Twitter Account to Follow</h2>
                <AddTwitterAccountForm />
              </div>

              {/* Followed accounts list */}
              <div className="bg-card rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Followed Accounts</h2>
                <FollowedAccountsList />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { generateViralTweet } from '@/lib/ai/viral-tweet-generator';
import {
  getUserTokenBalance,
  canAffordGeneration,
  hasPremiumAccess,
  deductGenerationCost
} from '@/lib/web3/token-service';
// Copy.AI pricing configuration (moved from web3/config to avoid client-side imports in API routes)
const COPY_CONFIG = {
  GENERATION_COST: parseFloat(process.env.COPY_GENERATION_COST || '0.5'),
  THEMES: {
    MEME: 'meme',
    DEGEN: 'degen',
    VC_BAIT: 'vc_bait',
    GENERAL: 'general',
  },
} as const;
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';

const generateLogger = logger.child({ component: 'CopyAIGenerate' });

// Request validation schema
const generateRequestSchema = z.object({
  prompt: z.string().min(1).max(500),
  theme: z.enum(['general', 'meme', 'degen', 'vc_bait']),
  context: z.object({
    type: z.enum(['twitter_post', 'twitter_handle']),
    content: z.string(),
    metadata: z.object({
      author: z.string().optional(),
      engagement: z.object({
        likes: z.number(),
        retweets: z.number(),
        replies: z.number(),
      }).optional(),
      created_at: z.string().optional(),
    }).optional(),
  }).optional(),
});

export async function POST(request: NextRequest) {
  const requestId = `generate-${Date.now()}`;
  generateLogger.info('Tweet generation request received', { requestId });

  try {
    // Parse and validate request body
    const body = await request.json();
    const { prompt, theme, context } = generateRequestSchema.parse(body);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      generateLogger.warn('Unauthorized generation attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    generateLogger.info('Processing generation request', {
      userId: user.id,
      prompt: prompt.substring(0, 50) + '...',
      theme,
      hasContext: !!context,
      contextType: context?.type,
      requestId
    });

    // Check if user has premium access (unlimited generations)
    const hasPremium = await hasPremiumAccess(user.id);

    if (!hasPremium) {
      // Check if user can afford generation
      const canAfford = await canAffordGeneration(user.id);

      if (!canAfford) {
        const balance = await getUserTokenBalance(user.id);
        generateLogger.warn('Insufficient balance for generation', {
          userId: user.id,
          balance: balance.balance,
          required: COPY_CONFIG.GENERATION_COST,
          requestId
        });

        return NextResponse.json(
          {
            error: 'Insufficient $COPY balance',
            required: COPY_CONFIG.GENERATION_COST,
            current: balance.balance
          },
          { status: 402 } // Payment Required
        );
      }
    }

    // Generate the viral tweet
    const generatedTweet = await generateViralTweet(prompt, theme, requestId, context);

    // Store the generated tweet in database
    const { data: tweetRecord, error: insertError } = await supabase
      .from('buddychip_generated_tweets')
      .insert({
        user_id: user.id,
        prompt,
        generated_content: generatedTweet.content,
        theme,
        cost_paid: hasPremium ? 0 : COPY_CONFIG.GENERATION_COST,
        viral_score: generatedTweet.viralScore,
      })
      .select()
      .single();

    if (insertError) {
      generateLogger.error('Error storing generated tweet', {
        userId: user.id,
        insertError,
        requestId
      });
      return NextResponse.json(
        { error: 'Failed to store generated tweet' },
        { status: 500 }
      );
    }

    // Deduct cost if not premium user
    if (!hasPremium) {
      try {
        await deductGenerationCost(user.id, tweetRecord.id.toString());
        generateLogger.info('Generation cost deducted', {
          userId: user.id,
          cost: COPY_CONFIG.GENERATION_COST,
          tweetId: tweetRecord.id,
          requestId
        });
      } catch (costError) {
        generateLogger.error('Error deducting generation cost', {
          userId: user.id,
          costError,
          requestId
        });
        // Note: Tweet is already generated and stored, so we don't fail here
        // This could be handled by a background job to retry
      }
    }

    // Get updated balance
    const updatedBalance = await getUserTokenBalance(user.id);

    generateLogger.info('Tweet generation completed successfully', {
      userId: user.id,
      tweetId: tweetRecord.id,
      viralScore: generatedTweet.viralScore,
      newBalance: updatedBalance.balance,
      requestId
    });

    return NextResponse.json({
      success: true,
      tweet: {
        id: tweetRecord.id,
        content: generatedTweet.content,
        theme: generatedTweet.theme,
        viralScore: generatedTweet.viralScore,
        hashtags: generatedTweet.hashtags,
        estimatedEngagement: generatedTweet.estimatedEngagement,
        costPaid: hasPremium ? 0 : COPY_CONFIG.GENERATION_COST,
        createdAt: tweetRecord.created_at,
      },
      balance: updatedBalance,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      generateLogger.warn('Invalid request data', { error: error.errors, requestId });
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    generateLogger.error('Unexpected error in tweet generation', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's generated tweets
    const { data: tweets, error: tweetsError } = await supabase
      .from('buddychip_generated_tweets')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(20);

    if (tweetsError) {
      generateLogger.error('Error fetching user tweets', {
        userId: user.id,
        tweetsError
      });
      return NextResponse.json(
        { error: 'Failed to fetch tweets' },
        { status: 500 }
      );
    }

    // Get user balance
    const balance = await getUserTokenBalance(user.id);
    const hasPremium = await hasPremiumAccess(user.id);

    return NextResponse.json({
      tweets: tweets || [],
      balance,
      hasPremiumAccess: hasPremium,
      generationCost: COPY_CONFIG.GENERATION_COST,
    });

  } catch (error) {
    generateLogger.error('Error in GET /api/copy-ai/generate', { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

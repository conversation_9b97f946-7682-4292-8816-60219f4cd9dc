import { createSupabaseServerClient } from '@/lib/supabase/server';
import { logger } from '@/lib/utils/logger';
import { PublicKey } from '@solana/web3.js';
import { getCopyTokenBalance} from '@/lib/solana/token-utils';
import { getAccountBalance, solanaConnection } from '@/lib/solana/connection';

// Copy.AI pricing configuration (moved here to avoid client-side imports in server code)
const COPY_CONFIG = {
  GENERATION_COST: parseFloat(process.env.COPY_GENERATION_COST || '0.5'),
  STAKING_MINIMUM: parseFloat(process.env.COPY_STAKING_MINIMUM || '100'),
} as const;

const tokenLogger = logger.child({ component: 'TokenService' });

export interface TokenBalance {
  balance: number;
  stakedAmount: number;
  totalEarned: number;
  totalSpent: number;
  walletAddress?: string;
}

export interface TokenTransaction {
  id: string;
  type: 'generation_cost' | 'shill_reward' | 'leaderboard_reward' | 'stake' | 'unstake';
  amount: number;
  relatedTweetId?: string;
  blockchainTxHash?: string;
  description: string;
  createdAt: string;
}

/**
 * Get user's token balance and staking information
 */
export async function getUserTokenBalance(userId: string): Promise<TokenBalance> {
  const supabase = await createSupabaseServerClient();

  tokenLogger.debug('Fetching token balance', { userId });

  const { data, error } = await supabase
    .from('buddychip_copy_tokens')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
    tokenLogger.error('Error fetching token balance', { userId, error });
    throw new Error('Failed to fetch token balance');
  }

  // If no record exists, create one with default values
  if (!data) {
    const { error: insertError } = await supabase
      .from('buddychip_copy_tokens')
      .insert({
        user_id: userId,
        balance: 0,
        staked_amount: 0,
        total_earned: 0,
        total_spent: 0,
      })
      .select()
      .single();

    if (insertError) {
      tokenLogger.error('Error creating token record', { userId, insertError });
      throw new Error('Failed to create token record');
    }

    return {
      balance: 0,
      stakedAmount: 0,
      totalEarned: 0,
      totalSpent: 0,
      walletAddress: undefined,
    };
  }

  return {
    balance: parseFloat(data.balance || '0'),
    stakedAmount: parseFloat(data.staked_amount || '0'),
    totalEarned: parseFloat(data.total_earned || '0'),
    totalSpent: parseFloat(data.total_spent || '0'),
    walletAddress: data.wallet_address,
  };
}

/**
 * Check if user has sufficient balance for tweet generation
 */
export async function canAffordGeneration(userId: string): Promise<boolean> {
  const balance = await getUserTokenBalance(userId);
  return balance.balance >= COPY_CONFIG.GENERATION_COST;
}

/**
 * Check if user has premium access through staking
 */
export async function hasPremiumAccess(userId: string): Promise<boolean> {
  const supabase = await createSupabaseServerClient();

  const { data, error } = await supabase
    .from('buddychip_staking')
    .select('premium_access_until')
    .eq('user_id', userId)
    .eq('is_active', true)
    .gte('premium_access_until', new Date().toISOString())
    .single();

  if (error) {
    tokenLogger.debug('No active premium access found', { userId });
    return false;
  }

  return !!data;
}

/**
 * Deduct tokens for tweet generation
 */
export async function deductGenerationCost(
  userId: string,
  tweetId: string,
  cost: number = COPY_CONFIG.GENERATION_COST
): Promise<void> {
  const supabase = await createSupabaseServerClient();

  tokenLogger.info('Deducting generation cost', { userId, tweetId, cost });

  // Start a transaction
  const { data: currentBalance, error: balanceError } = await supabase
    .from('buddychip_copy_tokens')
    .select('balance, total_spent')
    .eq('user_id', userId)
    .single();

  if (balanceError || !currentBalance) {
    throw new Error('Failed to fetch current balance');
  }

  const newBalance = parseFloat(currentBalance.balance) - cost;
  const newTotalSpent = parseFloat(currentBalance.total_spent || '0') + cost;

  if (newBalance < 0) {
    throw new Error('Insufficient balance');
  }

  // Update balance
  const { error: updateError } = await supabase
    .from('buddychip_copy_tokens')
    .update({
      balance: newBalance,
      total_spent: newTotalSpent,
    })
    .eq('user_id', userId);

  if (updateError) {
    tokenLogger.error('Error updating balance', { userId, updateError });
    throw new Error('Failed to update balance');
  }

  // Record transaction
  await recordTransaction(
    userId,
    'generation_cost',
    cost,
    tweetId,
    `Tweet generation cost`
  );
}

/**
 * Award tokens for shill-to-earn activity
 */
export async function awardShillReward(
  userId: string,
  tweetId: string,
  amount: number
): Promise<void> {
  const supabase = await createSupabaseServerClient();

  tokenLogger.info('Awarding shill reward', { userId, tweetId, amount });

  const { data: currentBalance, error: balanceError } = await supabase
    .from('buddychip_copy_tokens')
    .select('balance, total_earned')
    .eq('user_id', userId)
    .single();

  if (balanceError || !currentBalance) {
    throw new Error('Failed to fetch current balance');
  }

  const newBalance = parseFloat(currentBalance.balance) + amount;
  const newTotalEarned = parseFloat(currentBalance.total_earned || '0') + amount;

  const { error: updateError } = await supabase
    .from('buddychip_copy_tokens')
    .update({
      balance: newBalance,
      total_earned: newTotalEarned,
    })
    .eq('user_id', userId);

  if (updateError) {
    tokenLogger.error('Error updating balance for reward', { userId, updateError });
    throw new Error('Failed to update balance');
  }

  // Record transaction
  await recordTransaction(
    userId,
    'shill_reward',
    amount,
    tweetId,
    `Shill-to-earn reward`
  );
}

/**
 * Record a token transaction
 */
async function recordTransaction(
  userId: string,
  type: TokenTransaction['type'],
  amount: number,
  relatedTweetId?: string,
  description?: string,
  blockchainTxHash?: string
): Promise<void> {
  const supabase = await createSupabaseServerClient();

  const { error } = await supabase
    .from('buddychip_token_transactions')
    .insert({
      user_id: userId,
      transaction_type: type,
      amount,
      related_tweet_id: relatedTweetId,
      blockchain_tx_hash: blockchainTxHash,
      description,
    });

  if (error) {
    tokenLogger.error('Error recording transaction', { userId, type, amount, error });
    throw new Error('Failed to record transaction');
  }
}

/**
 * Get user's transaction history
 */
export async function getUserTransactions(
  userId: string,
  limit: number = 50
): Promise<TokenTransaction[]> {
  const supabase = await createSupabaseServerClient();

  const { data, error } = await supabase
    .from('buddychip_token_transactions')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    tokenLogger.error('Error fetching transactions', { userId, error });
    throw new Error('Failed to fetch transactions');
  }

  return data.map(tx => ({
    id: tx.id.toString(),
    type: tx.transaction_type,
    amount: parseFloat(tx.amount),
    relatedTweetId: tx.related_tweet_id?.toString(),
    blockchainTxHash: tx.blockchain_tx_hash,
    description: tx.description,
    createdAt: tx.created_at,
  }));
}

/**
 * Get on-chain Solana token balance for a wallet
 */
export async function getOnChainTokenBalance(walletAddress: string): Promise<{
  solBalance: number;
  copyTokenBalance: number;
}> {
  try {
    const publicKey = new PublicKey(walletAddress);

    tokenLogger.debug('Fetching on-chain balances', { walletAddress });

    const [solBalance, copyTokenBalance] = await Promise.all([
      getAccountBalance(publicKey, solanaConnection),
      getCopyTokenBalance(publicKey, solanaConnection),
    ]);

    tokenLogger.info('On-chain balances fetched', {
      walletAddress,
      solBalance,
      copyTokenBalance,
    });

    return {
      solBalance,
      copyTokenBalance,
    };
  } catch (error) {
    tokenLogger.error('Error fetching on-chain balances', { walletAddress, error });
    throw new Error('Failed to fetch on-chain balances');
  }
}

/**
 * Update user's wallet address in database
 */
export async function updateUserWalletAddress(
  userId: string,
  walletAddress: string
): Promise<void> {
  const supabase = await createSupabaseServerClient();

  tokenLogger.info('Updating user wallet address', { userId, walletAddress });

  // Validate the wallet address
  try {
    new PublicKey(walletAddress);
  } catch (error) {
    throw new Error('Invalid Solana wallet address');
  }

  const { error } = await supabase
    .from('buddychip_copy_tokens')
    .upsert({
      user_id: userId,
      wallet_address: walletAddress,
      balance: 0,
      staked_amount: 0,
      total_earned: 0,
      total_spent: 0,
    }, {
      onConflict: 'user_id',
      ignoreDuplicates: false,
    });

  if (error) {
    tokenLogger.error('Error updating wallet address', { userId, walletAddress, error });
    throw new Error('Failed to update wallet address');
  }
}

/**
 * Sync on-chain balance with database (for future blockchain integration)
 */
export async function syncOnChainBalance(userId: string): Promise<void> {
  const supabase = await createSupabaseServerClient();

  tokenLogger.debug('Syncing on-chain balance', { userId });

  const { data: userToken, error } = await supabase
    .from('buddychip_copy_tokens')
    .select('wallet_address')
    .eq('user_id', userId)
    .single();

  if (error || !userToken?.wallet_address) {
    tokenLogger.debug('No wallet address found for user', { userId });
    return;
  }

  try {
    const onChainBalances = await getOnChainTokenBalance(userToken.wallet_address);

    tokenLogger.info('On-chain balance sync completed', {
      userId,
      walletAddress: userToken.wallet_address,
      onChainBalances,
    });

    // Note: For now, we're just logging the on-chain balance
    // In the future, we might want to sync this with the database
    // or use it for verification purposes
  } catch (error) {
    tokenLogger.error('Error syncing on-chain balance', { userId, error });
    // Don't throw here - this is a background sync operation
  }
}

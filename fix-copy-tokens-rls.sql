-- Fix RLS policies for buddychip_copy_tokens table
-- Run this in your Supabase SQL Editor to fix the token balance error

-- First, check if the table exists and has RLS enabled
SELECT 
  schemaname, 
  tablename, 
  rowsecurity 
FROM pg_tables 
WHERE tablename = 'buddychip_copy_tokens';

-- Check existing policies
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'buddychip_copy_tokens';

-- Enable RLS if not already enabled
ALTER TABLE buddychip_copy_tokens ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view their own copy tokens" ON buddychip_copy_tokens;
DROP POLICY IF EXISTS "Users can insert their own copy tokens" ON buddychip_copy_tokens;
DROP POLICY IF EXISTS "Users can update their own copy tokens" ON buddychip_copy_tokens;
DROP POLICY IF EXISTS "Users can delete their own copy tokens" ON buddychip_copy_tokens;

-- Create comprehensive RLS policies for buddychip_copy_tokens
CREATE POLICY "Users can view their own copy tokens" 
ON buddychip_copy_tokens
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own copy tokens" 
ON buddychip_copy_tokens
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own copy tokens" 
ON buddychip_copy_tokens
FOR UPDATE 
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own copy tokens" 
ON buddychip_copy_tokens
FOR DELETE 
USING (auth.uid() = user_id);

-- Also allow service role to manage tokens (for system operations)
CREATE POLICY "Service role can manage all copy tokens" 
ON buddychip_copy_tokens
FOR ALL 
USING (auth.role() = 'service_role');

-- Verify the policies were created
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd
FROM pg_policies 
WHERE tablename = 'buddychip_copy_tokens'
ORDER BY policyname;

-- Test that the table is accessible
SELECT 
  'Table accessible' as status,
  COUNT(*) as record_count
FROM buddychip_copy_tokens;

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Enable ESLint during builds
    ignoreDuringBuilds: false,
  },
typescript: {
    // Enable TypeScript error checking during builds
    ignoreBuildErrors: false,
  },
  webpack: (config) => {
    // Fix for RainbowKit and wagmi
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };
    return config;
  },
};

module.exports = nextConfig;
